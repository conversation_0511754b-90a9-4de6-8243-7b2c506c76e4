// 星运测评 - 主要JavaScript功能
// 作者: AI Assistant
// 版本: 1.0.0

// 全局变量
let currentTest = null;
let testResults = {};
let userProfile = {};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 应用初始化
function initializeApp() {
    setupNavigation();
    setupModal();
    setupTestCards();
    setupZodiacButtons();
    loadUserProfile();
    generateDailyFortune();
}

// 导航功能设置
function setupNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }
    
    // 平滑滚动到指定部分
    const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            scrollToSection(targetId);
            
            // 关闭移动端菜单
            if (navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
            }
        });
    });
}

// 滚动到指定部分
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const offsetTop = section.offsetTop - 80; // 考虑导航栏高度
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// 模态框设置
function setupModal() {
    const modal = document.getElementById('testModal');
    const closeBtn = document.querySelector('.close');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            closeModal();
        });
    }
    
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
    }
}

// 打开模态框
function openModal(content) {
    const modal = document.getElementById('testModal');
    const modalBody = document.getElementById('modalBody');
    
    if (modal && modalBody) {
        modalBody.innerHTML = content;
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('testModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// 测评卡片设置
function setupTestCards() {
    const testCards = document.querySelectorAll('.test-card');
    testCards.forEach(card => {
        const testBtn = card.querySelector('.test-btn');
        if (testBtn) {
            testBtn.addEventListener('click', function() {
                const testType = card.getAttribute('data-test');
                startTest(testType);
            });
        }
    });
}

// 开始测评
function startTest(testType) {
    currentTest = testType;
    
    switch(testType) {
        case 'mbti':
            startMBTITest();
            break;
        case 'zodiac':
            startZodiacAnalysis();
            break;
        case 'tarot':
            startTarotReading();
            break;
        case 'bloodtype':
            startBloodTypeAnalysis();
            break;
        case 'palmistry':
            startPalmistryAnalysis();
            break;
        case 'iching':
            startIChingDivination();
            break;
        default:
            showMessage('该测评功能正在开发中，敬请期待！');
    }
}

// 显示消息
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.innerHTML = `
        <div class="message-content">
            <p>${message}</p>
            <button onclick="this.parentElement.parentElement.remove()">确定</button>
        </div>
    `;
    
    messageDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        z-index: 3000;
        text-align: center;
    `;
    
    document.body.appendChild(messageDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentElement) {
            messageDiv.remove();
        }
    }, 3000);
}

// 星座按钮设置
function setupZodiacButtons() {
    const zodiacButtons = document.querySelectorAll('.zodiac-btn');
    zodiacButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除其他按钮的active类
            zodiacButtons.forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active类
            this.classList.add('active');
            
            const sign = this.getAttribute('data-sign');
            showDailyFortune(sign);
        });
    });
}

// 加载用户配置
function loadUserProfile() {
    const saved = localStorage.getItem('xingyun_user_profile');
    if (saved) {
        try {
            userProfile = JSON.parse(saved);
        } catch (e) {
            console.error('加载用户配置失败:', e);
            userProfile = {};
        }
    }
}

// 保存用户配置
function saveUserProfile() {
    try {
        localStorage.setItem('xingyun_user_profile', JSON.stringify(userProfile));
    } catch (e) {
        console.error('保存用户配置失败:', e);
    }
}

// 保存测评结果
function saveTestResult(testType, result) {
    if (!testResults[testType]) {
        testResults[testType] = [];
    }
    
    result.timestamp = new Date().toISOString();
    testResults[testType].push(result);
    
    // 只保留最近10次结果
    if (testResults[testType].length > 10) {
        testResults[testType] = testResults[testType].slice(-10);
    }
    
    try {
        localStorage.setItem('xingyun_test_results', JSON.stringify(testResults));
    } catch (e) {
        console.error('保存测评结果失败:', e);
    }
}

// 加载测评结果
function loadTestResults() {
    const saved = localStorage.getItem('xingyun_test_results');
    if (saved) {
        try {
            testResults = JSON.parse(saved);
        } catch (e) {
            console.error('加载测评结果失败:', e);
            testResults = {};
        }
    }
}

// 工具函数：获取随机数
function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 工具函数：获取随机元素
function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

// 工具函数：格式化日期
function formatDate(date) {
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    return date.toLocaleDateString('zh-CN', options);
}

// 初始化加载测评结果
loadTestResults();

// ==================== MBTI 性格测评系统 ====================

// MBTI测评问题
const mbtiQuestions = [
    {
        question: "在社交聚会中，你更倾向于：",
        options: [
            { text: "主动与很多人交谈，成为焦点", type: "E" },
            { text: "与少数几个熟悉的人深入交流", type: "I" }
        ]
    },
    {
        question: "当面对新信息时，你更关注：",
        options: [
            { text: "具体的事实和细节", type: "S" },
            { text: "可能性和潜在含义", type: "N" }
        ]
    },
    {
        question: "做决定时，你更依赖：",
        options: [
            { text: "逻辑分析和客观标准", type: "T" },
            { text: "个人价值观和他人感受", type: "F" }
        ]
    },
    {
        question: "你更喜欢：",
        options: [
            { text: "制定详细计划并按计划执行", type: "J" },
            { text: "保持灵活性，随机应变", type: "P" }
        ]
    },
    {
        question: "在工作中，你更愿意：",
        options: [
            { text: "与团队成员频繁互动协作", type: "E" },
            { text: "独立思考和专注工作", type: "I" }
        ]
    },
    {
        question: "学习新技能时，你更喜欢：",
        options: [
            { text: "通过实践和具体例子", type: "S" },
            { text: "理解理论和概念框架", type: "N" }
        ]
    },
    {
        question: "处理冲突时，你更倾向于：",
        options: [
            { text: "直接指出问题，寻求解决方案", type: "T" },
            { text: "考虑各方感受，寻求和谐", type: "F" }
        ]
    },
    {
        question: "对于截止日期，你通常：",
        options: [
            { text: "提前完成，避免最后时刻的压力", type: "J" },
            { text: "在截止日期前完成，享受时间压力", type: "P" }
        ]
    },
    {
        question: "休息时间，你更愿意：",
        options: [
            { text: "参加聚会或户外活动", type: "E" },
            { text: "在家阅读或独自思考", type: "I" }
        ]
    },
    {
        question: "面对复杂问题时，你更关注：",
        options: [
            { text: "问题的具体表现和症状", type: "S" },
            { text: "问题背后的模式和趋势", type: "N" }
        ]
    },
    {
        question: "评价一个想法时，你首先考虑：",
        options: [
            { text: "这个想法是否合理有效", type: "T" },
            { text: "这个想法对人们的影响", type: "F" }
        ]
    },
    {
        question: "你更喜欢的工作环境是：",
        options: [
            { text: "结构化、有明确规则的环境", type: "J" },
            { text: "灵活多变、充满可能性的环境", type: "P" }
        ]
    },
    {
        question: "在团队讨论中，你更可能：",
        options: [
            { text: "积极发言，分享自己的观点", type: "E" },
            { text: "仔细倾听，深思熟虑后发言", type: "I" }
        ]
    },
    {
        question: "解决问题时，你更依赖：",
        options: [
            { text: "过往经验和已验证的方法", type: "S" },
            { text: "创新思维和新颖方法", type: "N" }
        ]
    },
    {
        question: "做重要决定时，你更重视：",
        options: [
            { text: "客观数据和逻辑推理", type: "T" },
            { text: "内心感受和价值判断", type: "F" }
        ]
    },
    {
        question: "你更喜欢：",
        options: [
            { text: "按既定计划有序进行", type: "J" },
            { text: "根据情况灵活调整", type: "P" }
        ]
    }
];

// MBTI人格类型描述
const mbtiTypes = {
    "INTJ": {
        name: "建筑师",
        description: "富有想象力和战略性的思想家，一切皆在计划之中。",
        traits: {
            "独立性": 90,
            "创新能力": 85,
            "逻辑思维": 88,
            "计划性": 92
        },
        strengths: ["战略思维", "独立自主", "创新能力", "目标导向"],
        weaknesses: ["过于理想化", "不善表达情感", "容易忽视细节", "可能显得冷漠"],
        careers: ["科学家", "工程师", "建筑师", "战略顾问", "研究员"],
        famous: ["埃隆·马斯克", "尼古拉·特斯拉", "艾萨克·牛顿"]
    },
    "INTP": {
        name: "思想家",
        description: "具有创新精神的发明家，对知识有着不可抑制的渴望。",
        traits: {
            "分析能力": 95,
            "创造力": 88,
            "独立性": 85,
            "灵活性": 80
        },
        strengths: ["逻辑分析", "理论思维", "客观公正", "适应能力强"],
        weaknesses: ["缺乏实用性", "难以做决定", "不善社交", "容易拖延"],
        careers: ["哲学家", "科学家", "程序员", "数学家", "理论研究员"],
        famous: ["阿尔伯特·爱因斯坦", "查尔斯·达尔文", "比尔·盖茨"]
    },
    "ENTJ": {
        name: "指挥官",
        description: "大胆、富有想象力、意志强烈的领导者，总能找到或创造解决方法。",
        traits: {
            "领导力": 95,
            "决策能力": 90,
            "目标导向": 92,
            "沟通能力": 85
        },
        strengths: ["天生领导", "高效执行", "战略规划", "自信果断"],
        weaknesses: ["过于强势", "缺乏耐心", "忽视他人感受", "完美主义"],
        careers: ["CEO", "企业家", "律师", "政治家", "管理顾问"],
        famous: ["史蒂夫·乔布斯", "拿破仑", "玛格丽特·撒切尔"]
    },
    "ENTP": {
        name: "辩论家",
        description: "聪明好奇的思想家，不会拒绝智力上的挑战。",
        traits: {
            "创新能力": 92,
            "适应性": 88,
            "沟通能力": 90,
            "好奇心": 95
        },
        strengths: ["创新思维", "适应能力强", "善于辩论", "充满热情"],
        weaknesses: ["容易分心", "不善处理细节", "可能显得争强好胜", "难以坚持"],
        careers: ["企业家", "发明家", "记者", "营销专家", "咨询师"],
        famous: ["托马斯·爱迪生", "本杰明·富兰克林", "马克·吐温"]
    },
    "INFJ": {
        name: "提倡者",
        description: "安静而神秘，同时鼓舞他人的理想主义者。",
        traits: {
            "洞察力": 92,
            "同理心": 95,
            "创造力": 85,
            "理想主义": 90
        },
        strengths: ["深刻洞察", "富有同情心", "创造性思维", "坚持原则"],
        weaknesses: ["过于敏感", "完美主义", "容易倦怠", "难以处理冲突"],
        careers: ["心理咨询师", "作家", "艺术家", "社会工作者", "教师"],
        famous: ["甘地", "马丁·路德·金", "柏拉图"]
    },
    "INFP": {
        name: "调停者",
        description: "诗意、善良、利他的人，总是热切地为正义事业而努力。",
        traits: {
            "创造力": 90,
            "同理心": 92,
            "价值观": 95,
            "灵活性": 85
        },
        strengths: ["富有创意", "善解人意", "忠于价值观", "适应能力强"],
        weaknesses: ["过于理想化", "容易情绪化", "难以批评他人", "缺乏实用性"],
        careers: ["作家", "艺术家", "心理学家", "社会工作者", "音乐家"],
        famous: ["威廉·莎士比亚", "J.R.R.托尔金", "约翰·列侬"]
    },
    "ENFJ": {
        name: "主人公",
        description: "富有魅力、鼓舞人心的领导者，有能力让听众着迷。",
        traits: {
            "领导力": 88,
            "同理心": 95,
            "沟通能力": 92,
            "影响力": 90
        },
        strengths: ["天生的领导者", "善于激励他人", "优秀的沟通者", "富有同情心"],
        weaknesses: ["过于在意他人看法", "容易忽视自己需求", "可能过于理想化", "难以做艰难决定"],
        careers: ["教师", "政治家", "心理咨询师", "人力资源", "社会活动家"],
        famous: ["奥普拉·温弗瑞", "巴拉克·奥巴马", "马丁·路德·金"]
    },
    "ENFP": {
        name: "竞选者",
        description: "热情、有创造力、社交能力强的自由精神，总能找到理由微笑。",
        traits: {
            "热情": 95,
            "创造力": 90,
            "社交能力": 92,
            "适应性": 88
        },
        strengths: ["充满热情", "富有创意", "善于社交", "适应能力强"],
        weaknesses: ["容易分心", "难以处理压力", "过于情绪化", "缺乏组织性"],
        careers: ["记者", "演员", "心理学家", "营销专家", "企业家"],
        famous: ["罗宾·威廉姆斯", "威尔·史密斯", "艾伦·德杰尼勒斯"]
    },
    "ISTJ": {
        name: "物流师",
        description: "实用主义的逻辑学家，忠诚可靠，不可或缺。",
        traits: {
            "责任感": 95,
            "组织能力": 92,
            "可靠性": 95,
            "实用性": 90
        },
        strengths: ["高度负责", "组织有序", "忠诚可靠", "注重细节"],
        weaknesses: ["抗拒变化", "过于严格", "难以表达情感", "可能显得固执"],
        careers: ["会计师", "律师", "医生", "工程师", "管理员"],
        famous: ["乔治·华盛顿", "沃伦·巴菲特", "安吉拉·默克尔"]
    },
    "ISFJ": {
        name: "守护者",
        description: "非常专注、温暖的守护者，时刻准备保护爱的人。",
        traits: {
            "同理心": 92,
            "责任感": 90,
            "支持性": 95,
            "细心": 88
        },
        strengths: ["富有同情心", "可靠支持", "注重细节", "忠诚奉献"],
        weaknesses: ["过于自我牺牲", "难以说不", "抗拒变化", "容易被忽视"],
        careers: ["护士", "教师", "社会工作者", "人力资源", "图书管理员"],
        famous: ["特蕾莎修女", "罗莎·帕克斯", "凯特·米德尔顿"]
    },
    "ESTJ": {
        name: "总经理",
        description: "出色的管理者，在管理事物或人员方面无与伦比。",
        traits: {
            "领导力": 90,
            "组织能力": 95,
            "决策能力": 88,
            "效率": 92
        },
        strengths: ["优秀的组织者", "果断决策", "高效执行", "负责任"],
        weaknesses: ["过于严格", "难以适应变化", "可能忽视他人感受", "固执己见"],
        careers: ["管理者", "军官", "法官", "银行家", "项目经理"],
        famous: ["温斯顿·丘吉尔", "亨利·福特", "维多利亚女王"]
    },
    "ESFJ": {
        name: "执政官",
        description: "极有同情心、受欢迎、总是热心帮助他人。",
        traits: {
            "同理心": 95,
            "社交能力": 90,
            "支持性": 92,
            "和谐性": 88
        },
        strengths: ["善于合作", "富有同情心", "忠诚可靠", "善于支持他人"],
        weaknesses: ["过于在意他人看法", "难以处理冲突", "可能忽视自己需求", "抗拒变化"],
        careers: ["教师", "护士", "销售代表", "活动策划", "客服代表"],
        famous: ["泰勒·斯威夫特", "惠特尼·休斯顿", "丹尼·德维托"]
    },
    "ISTP": {
        name: "鉴赏家",
        description: "大胆而实际的实验家，擅长使用各种工具。",
        traits: {
            "实用性": 92,
            "适应性": 90,
            "独立性": 88,
            "分析能力": 85
        },
        strengths: ["实用主义", "适应能力强", "冷静理性", "动手能力强"],
        weaknesses: ["难以表达情感", "可能显得冷漠", "不善长期规划", "容易厌倦"],
        careers: ["工程师", "技师", "飞行员", "外科医生", "运动员"],
        famous: ["克林特·伊斯特伍德", "布鲁斯·李", "迈克尔·乔丹"]
    },
    "ISFP": {
        name: "探险家",
        description: "灵活、迷人的艺术家，时刻准备探索新的可能性。",
        traits: {
            "创造力": 90,
            "灵活性": 92,
            "同理心": 88,
            "审美能力": 95
        },
        strengths: ["富有创意", "善解人意", "灵活适应", "审美敏感"],
        weaknesses: ["过于敏感", "难以处理压力", "缺乏组织性", "容易拖延"],
        careers: ["艺术家", "音乐家", "设计师", "摄影师", "治疗师"],
        famous: ["迈克尔·杰克逊", "奥黛丽·赫本", "鲍勃·迪伦"]
    },
    "ESTP": {
        name: "企业家",
        description: "聪明、精力充沛、善于感知的人，真正享受生活。",
        traits: {
            "适应性": 95,
            "社交能力": 90,
            "实用性": 88,
            "活力": 92
        },
        strengths: ["适应能力强", "善于社交", "实用主义", "充满活力"],
        weaknesses: ["缺乏长远规划", "容易冲动", "难以专注", "可能显得肤浅"],
        careers: ["销售员", "企业家", "运动员", "演员", "警察"],
        famous: ["唐纳德·特朗普", "欧内斯特·海明威", "布鲁斯·威利斯"]
    },
    "ESFP": {
        name: "娱乐家",
        description: "自发的、精力充沛、热情的人——生活在他们周围从不无聊。",
        traits: {
            "热情": 95,
            "社交能力": 92,
            "灵活性": 90,
            "乐观": 88
        },
        strengths: ["充满热情", "善于社交", "乐观积极", "灵活适应"],
        weaknesses: ["容易分心", "难以处理批评", "缺乏长远规划", "过于情绪化"],
        careers: ["演员", "音乐家", "销售员", "活动策划", "导游"],
        famous: ["玛丽莲·梦露", "埃尔顿·约翰", "威尔·史密斯"]
    }
};

// 开始MBTI测评
function startMBTITest() {
    const content = `
        <div class="test-form">
            <h2>🧠 MBTI性格测评</h2>
            <p>请根据你的真实想法选择最符合你的选项。测评共16题，大约需要10分钟。</p>
            <div id="mbtiQuestions"></div>
            <button class="submit-btn" onclick="calculateMBTIResult()" id="mbtiSubmit" disabled>查看结果</button>
        </div>
    `;

    openModal(content);
    renderMBTIQuestions();
}

// 渲染MBTI问题
function renderMBTIQuestions() {
    const container = document.getElementById('mbtiQuestions');
    if (!container) return;

    container.innerHTML = mbtiQuestions.map((q, index) => `
        <div class="question">
            <h4>${index + 1}. ${q.question}</h4>
            <div class="options">
                ${q.options.map((option, optIndex) => `
                    <label class="option">
                        <input type="radio" name="q${index}" value="${option.type}" onchange="checkMBTICompletion()">
                        ${option.text}
                    </label>
                `).join('')}
            </div>
        </div>
    `).join('');
}

// 检查MBTI测评完成度
function checkMBTICompletion() {
    const totalQuestions = mbtiQuestions.length;
    const answeredQuestions = document.querySelectorAll('input[type="radio"]:checked').length;
    const submitBtn = document.getElementById('mbtiSubmit');

    if (submitBtn) {
        submitBtn.disabled = answeredQuestions < totalQuestions;
        submitBtn.textContent = answeredQuestions < totalQuestions
            ? `已完成 ${answeredQuestions}/${totalQuestions} 题`
            : '查看结果';
    }
}

// 计算MBTI结果
function calculateMBTIResult() {
    const answers = {};
    const dimensions = ['E', 'I', 'S', 'N', 'T', 'F', 'J', 'P'];

    // 初始化计数
    dimensions.forEach(dim => answers[dim] = 0);

    // 统计答案
    mbtiQuestions.forEach((q, index) => {
        const selected = document.querySelector(`input[name="q${index}"]:checked`);
        if (selected) {
            answers[selected.value]++;
        }
    });

    // 确定人格类型
    const type =
        (answers['E'] > answers['I'] ? 'E' : 'I') +
        (answers['S'] > answers['N'] ? 'S' : 'N') +
        (answers['T'] > answers['F'] ? 'T' : 'F') +
        (answers['J'] > answers['P'] ? 'J' : 'P');

    const result = {
        type: type,
        scores: answers,
        ...mbtiTypes[type]
    };

    // 保存结果
    saveTestResult('mbti', result);

    // 显示结果
    showMBTIResult(result);
}

// 显示MBTI结果
function showMBTIResult(result) {
    const content = `
        <div class="result-card">
            <h3>🎉 你的MBTI人格类型</h3>
            <div class="personality-type">${result.type}</div>
            <h4>${result.name}</h4>
            <div class="description">${result.description}</div>

            <div class="traits">
                ${Object.entries(result.traits).map(([trait, score]) => `
                    <div class="trait">
                        <h4>${trait}</h4>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${score}%"></div>
                        </div>
                        <span>${score}%</span>
                    </div>
                `).join('')}
            </div>

            <div style="margin-top: 2rem; text-align: left;">
                <h4>💪 主要优势：</h4>
                <ul>
                    ${result.strengths.map(strength => `<li>${strength}</li>`).join('')}
                </ul>

                <h4>⚠️ 需要注意：</h4>
                <ul>
                    ${result.weaknesses.map(weakness => `<li>${weakness}</li>`).join('')}
                </ul>

                <h4>🎯 适合职业：</h4>
                <p>${result.careers.join('、')}</p>

                <h4>🌟 同类型名人：</h4>
                <p>${result.famous.join('、')}</p>
            </div>

            <button class="submit-btn" onclick="closeModal()" style="margin-top: 2rem;">完成测评</button>
        </div>
    `;

    const modalBody = document.getElementById('modalBody');
    if (modalBody) {
        modalBody.innerHTML = content;
    }
}

// ==================== 星座运势分析系统 ====================

// 星座信息数据
const zodiacData = {
    aries: {
        name: "白羊座",
        symbol: "♈",
        dates: "3月21日 - 4月19日",
        element: "火象",
        traits: ["热情", "勇敢", "直率", "冲动"],
        colors: ["红色", "橙色", "金色"]
    },
    taurus: {
        name: "金牛座",
        symbol: "♉",
        dates: "4月20日 - 5月20日",
        element: "土象",
        traits: ["稳重", "务实", "固执", "享受"],
        colors: ["绿色", "粉色", "棕色"]
    },
    gemini: {
        name: "双子座",
        symbol: "♊",
        dates: "5月21日 - 6月20日",
        element: "风象",
        traits: ["机智", "多变", "好奇", "善辩"],
        colors: ["黄色", "银色", "蓝色"]
    },
    cancer: {
        name: "巨蟹座",
        symbol: "♋",
        dates: "6月21日 - 7月22日",
        element: "水象",
        traits: ["温柔", "敏感", "顾家", "情绪化"],
        colors: ["白色", "银色", "海蓝色"]
    },
    leo: {
        name: "狮子座",
        symbol: "♌",
        dates: "7月23日 - 8月22日",
        element: "火象",
        traits: ["自信", "慷慨", "领导力", "爱表现"],
        colors: ["金色", "橙色", "红色"]
    },
    virgo: {
        name: "处女座",
        symbol: "♍",
        dates: "8月23日 - 9月22日",
        element: "土象",
        traits: ["完美主义", "细致", "实用", "挑剔"],
        colors: ["深蓝色", "灰色", "棕色"]
    },
    libra: {
        name: "天秤座",
        symbol: "♎",
        dates: "9月23日 - 10月22日",
        element: "风象",
        traits: ["和谐", "优雅", "犹豫", "公正"],
        colors: ["粉色", "浅蓝色", "绿色"]
    },
    scorpio: {
        name: "天蝎座",
        symbol: "♏",
        dates: "10月23日 - 11月21日",
        element: "水象",
        traits: ["神秘", "专注", "激情", "报复心"],
        colors: ["深红色", "黑色", "紫色"]
    },
    sagittarius: {
        name: "射手座",
        symbol: "♐",
        dates: "11月22日 - 12月21日",
        element: "火象",
        traits: ["自由", "乐观", "直率", "冒险"],
        colors: ["紫色", "蓝色", "绿色"]
    },
    capricorn: {
        name: "摩羯座",
        symbol: "♑",
        dates: "12月22日 - 1月19日",
        element: "土象",
        traits: ["务实", "有野心", "保守", "负责"],
        colors: ["黑色", "深绿色", "棕色"]
    },
    aquarius: {
        name: "水瓶座",
        symbol: "♒",
        dates: "1月20日 - 2月18日",
        element: "风象",
        traits: ["独立", "创新", "友善", "叛逆"],
        colors: ["蓝色", "银色", "紫色"]
    },
    pisces: {
        name: "双鱼座",
        symbol: "♓",
        dates: "2月19日 - 3月20日",
        element: "水象",
        traits: ["浪漫", "直觉", "同情心", "逃避"],
        colors: ["海绿色", "紫色", "银色"]
    }
};

// 运势模板数据
const fortuneTemplates = {
    overall: [
        "今天是充满机遇的一天，保持积极的心态会为你带来意想不到的收获。",
        "今日运势平稳，适合处理日常事务，避免做重大决定。",
        "今天可能会遇到一些小挑战，但你的智慧和经验会帮助你顺利度过。",
        "今日是展现你才华的好时机，勇敢地表达自己的想法。",
        "今天适合与他人合作，团队的力量会让你事半功倍。",
        "今日需要保持耐心，好事即将到来，不要急于求成。",
        "今天是反思和规划的好日子，为未来做好准备。",
        "今日充满创意和灵感，是开始新项目的绝佳时机。"
    ],
    love: [
        "单身的你今天可能会遇到心动的人，保持开放的心态。",
        "恋爱中的你们今天感情甜蜜，适合表达爱意。",
        "今天可能会与伴侣产生小摩擦，多一些理解和包容。",
        "今日是增进感情的好时机，不妨安排一次浪漫的约会。",
        "单身的你今天魅力四射，自信会让你更加吸引人。",
        "今天适合与伴侣深入交流，分享彼此的想法和感受。",
        "今日爱情运势一般，专注于自我提升会更有意义。",
        "今天可能会收到来自过去的消息，理性对待感情问题。"
    ],
    money: [
        "今天的财运不错，可能会有意外的收入或投资机会。",
        "今日需要谨慎理财，避免冲动消费。",
        "今天适合制定理财计划，为未来的财务安全做准备。",
        "今日可能会有额外的支出，提前做好预算。",
        "今天是投资学习的好时机，知识就是财富。",
        "今日财运平稳，保持现有的理财策略即可。",
        "今天可能会遇到赚钱的机会，但需要仔细评估风险。",
        "今日适合清理财务状况，整理账目和投资组合。"
    ],
    career: [
        "今天工作效率很高，是完成重要任务的好时机。",
        "今日可能会遇到工作上的挑战，保持冷静应对。",
        "今天适合与同事合作，团队协作会带来好结果。",
        "今日是展现领导能力的时候，勇敢承担责任。",
        "今天可能会收到工作上的好消息，保持期待。",
        "今日需要注意细节，小心谨慎地处理工作事务。",
        "今天适合学习新技能，提升自己的职业竞争力。",
        "今日工作压力可能较大，记得适当休息和放松。"
    ]
};

// 幸运色彩数据
const luckyColors = [
    { name: "活力红", color: "#FF6B6B", meaning: "增强行动力和自信" },
    { name: "智慧蓝", color: "#4ECDC4", meaning: "提升思考能力和冷静" },
    { name: "财富金", color: "#FFD93D", meaning: "招财进宝，事业顺利" },
    { name: "和谐绿", color: "#6BCF7F", meaning: "促进人际关系和健康" },
    { name: "浪漫粉", color: "#FF8A95", meaning: "增进爱情运势" },
    { name: "神秘紫", color: "#A8E6CF", meaning: "提升直觉和创造力" },
    { name: "稳重棕", color: "#D4A574", meaning: "增强稳定性和安全感" },
    { name: "纯净白", color: "#FFFFFF", meaning: "净化心灵，带来平静" },
    { name: "深邃黑", color: "#2C3E50", meaning: "增强专注力和神秘感" },
    { name: "温暖橙", color: "#FF9F43", meaning: "带来温暖和积极能量" }
];

// 开始星座分析
function startZodiacAnalysis() {
    const content = `
        <div class="test-form">
            <h2>⭐ 星座运势分析</h2>
            <p>请选择你的出生日期，我们将为你分析星座特质和今日运势。</p>

            <div class="question">
                <h4>请选择你的星座：</h4>
                <div class="zodiac-selection">
                    ${Object.entries(zodiacData).map(([key, data]) => `
                        <label class="zodiac-option">
                            <input type="radio" name="zodiacSign" value="${key}">
                            <div class="zodiac-card">
                                <div class="zodiac-symbol">${data.symbol}</div>
                                <div class="zodiac-name">${data.name}</div>
                                <div class="zodiac-dates">${data.dates}</div>
                            </div>
                        </label>
                    `).join('')}
                </div>
            </div>

            <button class="submit-btn" onclick="generateZodiacAnalysis()" id="zodiacSubmit" disabled>查看分析</button>
        </div>
    `;

    openModal(content);
    setupZodiacSelection();
}

// 设置星座选择
function setupZodiacSelection() {
    const radios = document.querySelectorAll('input[name="zodiacSign"]');
    const submitBtn = document.getElementById('zodiacSubmit');

    radios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (submitBtn) {
                submitBtn.disabled = false;
            }
        });
    });
}

// 生成星座分析
function generateZodiacAnalysis() {
    const selected = document.querySelector('input[name="zodiacSign"]:checked');
    if (!selected) return;

    const sign = selected.value;
    const zodiac = zodiacData[sign];
    const fortune = generateDailyFortuneData(sign);

    const result = {
        sign: sign,
        zodiac: zodiac,
        fortune: fortune,
        date: new Date().toLocaleDateString('zh-CN')
    };

    // 保存结果
    saveTestResult('zodiac', result);

    // 显示结果
    showZodiacAnalysis(result);
}

// 显示星座分析结果
function showZodiacAnalysis(result) {
    const { zodiac, fortune } = result;

    const content = `
        <div class="result-card">
            <h3>${zodiac.symbol} ${zodiac.name} 运势分析</h3>
            <div class="zodiac-info">
                <p><strong>日期范围：</strong>${zodiac.dates}</p>
                <p><strong>星座元素：</strong>${zodiac.element}</p>
                <p><strong>性格特质：</strong>${zodiac.traits.join('、')}</p>
            </div>

            <div class="fortune-content" style="margin-top: 2rem;">
                <div class="fortune-item">
                    <h4>💝 综合运势</h4>
                    <div class="fortune-stars">${generateStars(fortune.overall.stars)}</div>
                    <p>${fortune.overall.description}</p>
                </div>

                <div class="fortune-item">
                    <h4>💕 爱情运势</h4>
                    <div class="fortune-stars">${generateStars(fortune.love.stars)}</div>
                    <p>${fortune.love.description}</p>
                </div>

                <div class="fortune-item">
                    <h4>💰 财运</h4>
                    <div class="fortune-stars">${generateStars(fortune.money.stars)}</div>
                    <p>${fortune.money.description}</p>
                </div>

                <div class="fortune-item">
                    <h4>💼 事业运势</h4>
                    <div class="fortune-stars">${generateStars(fortune.career.stars)}</div>
                    <p>${fortune.career.description}</p>
                </div>
            </div>

            <div class="lucky-colors" style="margin-top: 2rem;">
                <h4>🎨 今日幸运色</h4>
                <div class="color-palette">
                    ${fortune.luckyColors.map(color => `
                        <div class="color-item" style="background-color: ${color.color};" title="${color.meaning}">
                            ${color.name}
                        </div>
                    `).join('')}
                </div>
            </div>

            <button class="submit-btn" onclick="closeModal()" style="margin-top: 2rem;">完成分析</button>
        </div>
    `;

    const modalBody = document.getElementById('modalBody');
    if (modalBody) {
        modalBody.innerHTML = content;
    }
}

// 生成每日运势数据
function generateDailyFortuneData(sign) {
    return {
        overall: {
            stars: getRandomInt(3, 5),
            description: getRandomElement(fortuneTemplates.overall)
        },
        love: {
            stars: getRandomInt(2, 5),
            description: getRandomElement(fortuneTemplates.love)
        },
        money: {
            stars: getRandomInt(2, 5),
            description: getRandomElement(fortuneTemplates.money)
        },
        career: {
            stars: getRandomInt(3, 5),
            description: getRandomElement(fortuneTemplates.career)
        },
        luckyColors: getRandomElements(luckyColors, 3)
    };
}

// 生成星星评级
function generateStars(count) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= count) {
            stars += '<span class="star">★</span>';
        } else {
            stars += '<span class="star empty">☆</span>';
        }
    }
    return stars;
}

// 获取随机元素数组
function getRandomElements(array, count) {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
}

// 显示每日运势（主页面使用）
function showDailyFortune(sign) {
    const zodiac = zodiacData[sign];
    const fortune = generateDailyFortuneData(sign);

    const fortuneDisplay = document.getElementById('fortuneDisplay');
    const fortuneTitle = document.getElementById('fortuneTitle');

    if (fortuneTitle) {
        fortuneTitle.textContent = `${zodiac.symbol} ${zodiac.name} - ${formatDate(new Date())}`;
    }

    // 更新运势内容
    updateFortuneContent('overall', fortune.overall);
    updateFortuneContent('love', fortune.love);
    updateFortuneContent('money', fortune.money);
    updateFortuneContent('career', fortune.career);

    // 更新幸运色
    const luckyColorsContainer = document.getElementById('luckyColors');
    if (luckyColorsContainer) {
        luckyColorsContainer.innerHTML = fortune.luckyColors.map(color => `
            <div class="color-item" style="background-color: ${color.color};" title="${color.meaning}">
                ${color.name}
            </div>
        `).join('');
    }

    if (fortuneDisplay) {
        fortuneDisplay.style.display = 'block';
    }
}

// 更新运势内容
function updateFortuneContent(type, data) {
    const starsElement = document.getElementById(`${type}Stars`);
    const fortuneElement = document.getElementById(`${type}Fortune`);

    if (starsElement) {
        starsElement.innerHTML = generateStars(data.stars);
    }

    if (fortuneElement) {
        fortuneElement.textContent = data.description;
    }
}

// 生成每日运势（页面加载时调用）
function generateDailyFortune() {
    // 可以根据用户设置的默认星座显示运势
    const defaultSign = userProfile.defaultZodiac || 'aries';
    // 这里不自动显示，等用户选择星座
}

// ==================== 塔罗牌占卜系统 ====================

// 塔罗牌数据（简化版，包含主要大阿卡纳牌）
const tarotCards = [
    {
        id: 0,
        name: "愚者",
        nameEn: "The Fool",
        upright: {
            keywords: ["新开始", "冒险", "自由", "纯真"],
            meaning: "代表新的开始和无限的可能性。现在是时候踏出舒适圈，拥抱未知的冒险。"
        },
        reversed: {
            keywords: ["鲁莽", "缺乏计划", "愚蠢", "风险"],
            meaning: "警告你不要过于冲动，需要更多的思考和计划再行动。"
        }
    },
    {
        id: 1,
        name: "魔术师",
        nameEn: "The Magician",
        upright: {
            keywords: ["意志力", "创造", "技能", "专注"],
            meaning: "你拥有实现目标所需的所有工具和能力。专注于你的意图，将想法转化为现实。"
        },
        reversed: {
            keywords: ["操控", "欺骗", "缺乏技能", "意志薄弱"],
            meaning: "可能存在自我欺骗或被他人操控的情况，需要重新审视自己的能力。"
        }
    },
    {
        id: 2,
        name: "女祭司",
        nameEn: "The High Priestess",
        upright: {
            keywords: ["直觉", "神秘", "潜意识", "智慧"],
            meaning: "相信你的直觉和内在智慧。答案就在你的内心深处，静心聆听内在的声音。"
        },
        reversed: {
            keywords: ["忽视直觉", "秘密", "缺乏内省", "表面"],
            meaning: "你可能忽视了内在的声音，或者有隐藏的信息需要被揭露。"
        }
    },
    {
        id: 3,
        name: "皇后",
        nameEn: "The Empress",
        upright: {
            keywords: ["丰饶", "母性", "创造力", "自然"],
            meaning: "代表丰富的创造力和滋养能力。是时候关注自己的创造性项目和人际关系。"
        },
        reversed: {
            keywords: ["依赖", "空虚", "缺乏成长", "不育"],
            meaning: "可能感到创造力枯竭或过度依赖他人，需要重新连接自己的内在力量。"
        }
    },
    {
        id: 4,
        name: "皇帝",
        nameEn: "The Emperor",
        upright: {
            keywords: ["权威", "结构", "控制", "稳定"],
            meaning: "需要建立秩序和结构。运用你的领导能力和权威来实现目标。"
        },
        reversed: {
            keywords: ["专制", "缺乏纪律", "不负责任", "僵化"],
            meaning: "可能过于控制或缺乏必要的纪律，需要在权威和灵活性之间找到平衡。"
        }
    },
    {
        id: 5,
        name: "教皇",
        nameEn: "The Hierophant",
        upright: {
            keywords: ["传统", "精神指导", "学习", "信仰"],
            meaning: "寻求精神指导和传统智慧。通过学习和遵循既定的道路来获得成长。"
        },
        reversed: {
            keywords: ["反叛", "非传统", "个人信仰", "限制"],
            meaning: "质疑传统和权威，寻找属于自己的精神道路和信仰体系。"
        }
    },
    {
        id: 6,
        name: "恋人",
        nameEn: "The Lovers",
        upright: {
            keywords: ["爱情", "关系", "选择", "和谐"],
            meaning: "代表重要的关系和人生选择。需要在心灵和理智之间找到平衡。"
        },
        reversed: {
            keywords: ["不和谐", "错误选择", "分离", "价值冲突"],
            meaning: "关系中可能存在问题，或者面临困难的选择，需要重新评估价值观。"
        }
    },
    {
        id: 7,
        name: "战车",
        nameEn: "The Chariot",
        upright: {
            keywords: ["胜利", "意志力", "控制", "前进"],
            meaning: "通过坚强的意志力和自我控制来克服障碍，取得胜利。"
        },
        reversed: {
            keywords: ["失控", "缺乏方向", "失败", "内在冲突"],
            meaning: "可能失去控制或方向感，需要重新整合内在的对立力量。"
        }
    },
    {
        id: 8,
        name: "力量",
        nameEn: "Strength",
        upright: {
            keywords: ["内在力量", "勇气", "耐心", "同情"],
            meaning: "真正的力量来自内心的勇气和同情心。用温柔而坚定的方式面对挑战。"
        },
        reversed: {
            keywords: ["软弱", "缺乏信心", "滥用力量", "恐惧"],
            meaning: "可能缺乏内在力量或滥用权力，需要重新找回内心的平衡。"
        }
    },
    {
        id: 9,
        name: "隐者",
        nameEn: "The Hermit",
        upright: {
            keywords: ["内省", "寻找", "指导", "智慧"],
            meaning: "需要独处和内省的时间。通过内在的探索来寻找答案和智慧。"
        },
        reversed: {
            keywords: ["孤立", "拒绝帮助", "迷失", "固执"],
            meaning: "可能过度孤立自己或拒绝他人的指导，需要重新连接外在世界。"
        }
    },
    {
        id: 10,
        name: "命运之轮",
        nameEn: "Wheel of Fortune",
        upright: {
            keywords: ["命运", "变化", "循环", "机会"],
            meaning: "生活中的重大转变即将到来。接受变化，抓住命运带来的机会。"
        },
        reversed: {
            keywords: ["厄运", "缺乏控制", "抗拒变化", "挫折"],
            meaning: "可能遇到挫折或抗拒必要的变化，需要接受生活的起伏。"
        }
    },
    {
        id: 11,
        name: "正义",
        nameEn: "Justice",
        upright: {
            keywords: ["公正", "平衡", "真相", "因果"],
            meaning: "寻求公正和平衡。你的行为将得到应有的回报，真相将被揭露。"
        },
        reversed: {
            keywords: ["不公", "偏见", "逃避责任", "不平衡"],
            meaning: "可能面临不公正的待遇或需要承担被忽视的责任。"
        }
    },
    {
        id: 12,
        name: "倒吊人",
        nameEn: "The Hanged Man",
        upright: {
            keywords: ["牺牲", "等待", "新视角", "放手"],
            meaning: "需要暂停和等待，从不同的角度看待问题。有时放手比坚持更有智慧。"
        },
        reversed: {
            keywords: ["无谓牺牲", "拖延", "抗拒", "自私"],
            meaning: "可能做出无意义的牺牲或过度拖延，需要采取行动。"
        }
    },
    {
        id: 13,
        name: "死神",
        nameEn: "Death",
        upright: {
            keywords: ["转变", "结束", "重生", "释放"],
            meaning: "代表重大的转变和新生。旧的必须结束，为新的开始让路。"
        },
        reversed: {
            keywords: ["抗拒变化", "停滞", "恐惧", "腐朽"],
            meaning: "抗拒必要的变化或改变，导致停滞不前。"
        }
    },
    {
        id: 14,
        name: "节制",
        nameEn: "Temperance",
        upright: {
            keywords: ["平衡", "节制", "和谐", "治愈"],
            meaning: "寻求生活中的平衡与和谐。通过耐心和节制来实现内在的治愈。"
        },
        reversed: {
            keywords: ["不平衡", "过度", "缺乏耐心", "冲突"],
            meaning: "生活失去平衡，可能过度沉溺于某些事物或缺乏耐心。"
        }
    },
    {
        id: 15,
        name: "恶魔",
        nameEn: "The Devil",
        upright: {
            keywords: ["束缚", "诱惑", "物质主义", "阴影"],
            meaning: "警告你可能被物质欲望或负面习惯所束缚。需要面对内在的阴影。"
        },
        reversed: {
            keywords: ["解脱", "觉醒", "克服诱惑", "自由"],
            meaning: "正在从束缚中解脱，克服诱惑，重获内在的自由。"
        }
    },
    {
        id: 16,
        name: "塔",
        nameEn: "The Tower",
        upright: {
            keywords: ["突然变化", "破坏", "启示", "解放"],
            meaning: "突如其来的变化将打破现状。虽然痛苦，但这是必要的解放。"
        },
        reversed: {
            keywords: ["避免灾难", "内在变化", "抗拒", "延迟"],
            meaning: "可能避免了外在的灾难，但内在的变化仍然必要。"
        }
    },
    {
        id: 17,
        name: "星星",
        nameEn: "The Star",
        upright: {
            keywords: ["希望", "灵感", "治愈", "指引"],
            meaning: "在黑暗之后迎来希望之光。相信未来，跟随你的灵感和直觉。"
        },
        reversed: {
            keywords: ["绝望", "缺乏信心", "失去方向", "幻灭"],
            meaning: "可能感到绝望或失去信心，需要重新找回内在的希望之光。"
        }
    },
    {
        id: 18,
        name: "月亮",
        nameEn: "The Moon",
        upright: {
            keywords: ["幻象", "恐惧", "潜意识", "直觉"],
            meaning: "事情可能不如表面所见。相信你的直觉，但要小心幻象和欺骗。"
        },
        reversed: {
            keywords: ["真相揭露", "克服恐惧", "清晰", "释放"],
            meaning: "幻象正在消散，真相即将揭露，恐惧正在被克服。"
        }
    },
    {
        id: 19,
        name: "太阳",
        nameEn: "The Sun",
        upright: {
            keywords: ["成功", "快乐", "活力", "成就"],
            meaning: "代表成功、快乐和生命力。一切都在向好的方向发展。"
        },
        reversed: {
            keywords: ["延迟成功", "缺乏活力", "过度自信", "内在快乐"],
            meaning: "成功可能延迟到来，或者需要寻找内在的快乐源泉。"
        }
    },
    {
        id: 20,
        name: "审判",
        nameEn: "Judgement",
        upright: {
            keywords: ["重生", "觉醒", "宽恕", "救赎"],
            meaning: "精神上的觉醒和重生。是时候宽恕过去，拥抱新的开始。"
        },
        reversed: {
            keywords: ["自我怀疑", "严厉批判", "拒绝宽恕", "错失机会"],
            meaning: "可能过于严厉地批判自己或他人，错失重生的机会。"
        }
    },
    {
        id: 21,
        name: "世界",
        nameEn: "The World",
        upright: {
            keywords: ["完成", "成就", "圆满", "成功"],
            meaning: "代表圆满的完成和巨大的成就。你已经达到了一个重要的里程碑。"
        },
        reversed: {
            keywords: ["未完成", "缺乏成就感", "延迟", "内在完整"],
            meaning: "目标可能尚未完成，或者需要寻找内在的完整感。"
        }
    }
];

// 塔罗牌占卜布局
const tarotSpreads = {
    threeCard: {
        name: "三张牌占卜",
        positions: [
            { name: "过去", description: "影响当前情况的过去因素" },
            { name: "现在", description: "当前的状况和挑战" },
            { name: "未来", description: "可能的结果和发展方向" }
        ]
    }
};

// 开始塔罗牌占卜
function startTarotReading() {
    const content = `
        <div class="test-form">
            <h2>🔮 塔罗牌占卜</h2>
            <p>静心思考你想要了解的问题，然后点击下方按钮开始占卜。</p>

            <div class="question">
                <h4>请在心中默念你的问题，然后选择占卜方式：</h4>
                <div class="tarot-spread-selection">
                    <div class="spread-option selected" data-spread="threeCard">
                        <h4>三张牌占卜</h4>
                        <p>探索过去、现在和未来的指引</p>
                        <div class="spread-preview">
                            <div class="card-placeholder">过去</div>
                            <div class="card-placeholder">现在</div>
                            <div class="card-placeholder">未来</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="question">
                <h4>请输入你想要占卜的问题（可选）：</h4>
                <textarea id="tarotQuestion" placeholder="例如：我在感情方面应该如何选择？" rows="3"></textarea>
            </div>

            <button class="submit-btn" onclick="drawTarotCards()">开始占卜</button>
        </div>
    `;

    openModal(content);
}

// 抽取塔罗牌
function drawTarotCards() {
    const question = document.getElementById('tarotQuestion')?.value || "未指定问题";
    const spread = tarotSpreads.threeCard;

    // 随机抽取3张不重复的牌
    const drawnCards = [];
    const usedIndices = new Set();

    while (drawnCards.length < 3) {
        const randomIndex = getRandomInt(0, tarotCards.length - 1);
        if (!usedIndices.has(randomIndex)) {
            usedIndices.add(randomIndex);
            const card = tarotCards[randomIndex];
            const isReversed = Math.random() < 0.3; // 30%概率逆位

            drawnCards.push({
                ...card,
                isReversed: isReversed,
                position: spread.positions[drawnCards.length]
            });
        }
    }

    const result = {
        question: question,
        spread: spread.name,
        cards: drawnCards,
        date: new Date().toISOString()
    };

    // 保存结果
    saveTestResult('tarot', result);

    // 显示结果
    showTarotReading(result);
}

// 显示塔罗牌占卜结果
function showTarotReading(result) {
    const content = `
        <div class="result-card">
            <h3>🔮 塔罗牌占卜结果</h3>
            <div class="tarot-question">
                <h4>占卜问题：</h4>
                <p>${result.question}</p>
            </div>

            <div class="tarot-cards">
                ${result.cards.map((card, index) => `
                    <div class="tarot-card-result">
                        <div class="card-position">
                            <h4>${card.position.name}</h4>
                            <p class="position-desc">${card.position.description}</p>
                        </div>

                        <div class="card-info">
                            <div class="card-name">
                                <h3>${card.name} ${card.isReversed ? '(逆位)' : ''}</h3>
                                <p class="card-name-en">${card.nameEn}</p>
                            </div>

                            <div class="card-meaning">
                                <div class="keywords">
                                    <strong>关键词：</strong>
                                    ${(card.isReversed ? card.reversed.keywords : card.upright.keywords).join('、')}
                                </div>
                                <div class="interpretation">
                                    <p>${card.isReversed ? card.reversed.meaning : card.upright.meaning}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>

            <div class="tarot-summary">
                <h4>🌟 综合解读</h4>
                <p>${generateTarotSummary(result.cards)}</p>
            </div>

            <button class="submit-btn" onclick="closeModal()" style="margin-top: 2rem;">完成占卜</button>
        </div>
    `;

    const modalBody = document.getElementById('modalBody');
    if (modalBody) {
        modalBody.innerHTML = content;
    }
}

// 生成塔罗牌综合解读
function generateTarotSummary(cards) {
    const summaries = [
        "从你抽到的牌来看，过去的经历为现在奠定了基础，当前的挑战需要你运用智慧来应对，未来充满了新的可能性。",
        "这三张牌显示了一个完整的故事：过去的影响正在塑造现在，而你现在的选择将决定未来的方向。",
        "牌面显示你正处在一个重要的转折点，过去的经验是你的财富，现在需要做出明智的决定来创造理想的未来。",
        "从整体来看，这是一个成长和转变的过程，每一步都有其意义，相信自己的直觉和内在智慧。",
        "这些牌提醒你要平衡过去的教训、现在的行动和未来的愿景，找到属于自己的道路。"
    ];

    return getRandomElement(summaries);
}

// ==================== 血型性格分析系统 ====================

// 血型性格数据
const bloodTypeData = {
    A: {
        name: "A型血",
        symbol: "🅰️",
        traits: {
            positive: ["细心谨慎", "有责任感", "善于合作", "追求完美", "有条理"],
            negative: ["过于敏感", "容易焦虑", "固执己见", "缺乏自信", "压抑情感"]
        },
        personality: {
            overview: "A型血的人通常性格温和、细心，具有强烈的责任感和团队合作精神。他们追求完美，注重细节，但有时可能过于敏感和焦虑。",
            work: "在工作中表现出色，善于规划和执行，适合需要细致和耐心的工作。但可能因为过于追求完美而给自己造成压力。",
            love: "在感情中比较被动，需要时间来建立信任。一旦投入感情，会非常专一和忠诚，但可能因为过于敏感而产生不安全感。",
            social: "善于倾听他人，是很好的朋友和伙伴。但在陌生环境中可能比较内向，需要时间来适应新的社交圈子。"
        },
        compatibility: {
            best: ["A型", "AB型"],
            good: ["O型"],
            challenging: ["B型"]
        },
        famous: ["村上春树", "宫崎骏", "张艺谋"],
        advice: "学会放松自己，不要过于追求完美。相信自己的能力，勇敢表达内心的想法。",
        luckyColors: ["蓝色", "白色", "绿色"],
        strengths: ["责任感强", "注重细节", "善于合作", "有条理", "忠诚可靠"],
        weaknesses: ["过于敏感", "容易焦虑", "缺乏自信", "固执", "压抑情感"]
    },
    B: {
        name: "B型血",
        symbol: "🅱️",
        traits: {
            positive: ["创造力强", "乐观开朗", "适应力强", "独立自主", "直率真诚"],
            negative: ["情绪化", "缺乏耐心", "不够细心", "容易冲动", "难以坚持"]
        },
        personality: {
            overview: "B型血的人通常性格开朗、富有创造力，具有很强的适应能力和独立精神。他们直率真诚，但有时可能过于情绪化和冲动。",
            work: "富有创意和想象力，适合需要创新思维的工作。但可能缺乏耐心处理细节工作，需要学会坚持和专注。",
            love: "在感情中比较主动和直接，容易陷入热恋。但也容易因为新鲜感消失而失去兴趣，需要学会经营长期关系。",
            social: "天生的社交达人，容易与人建立联系。但有时可能过于直率，需要注意表达方式，避免无意中伤害他人。"
        },
        compatibility: {
            best: ["B型", "AB型"],
            good: ["O型"],
            challenging: ["A型"]
        },
        famous: ["李小龙", "成龙", "周杰伦"],
        advice: "学会控制情绪，培养耐心和坚持力。在表达观点时要考虑他人的感受。",
        luckyColors: ["红色", "橙色", "黄色"],
        strengths: ["创造力强", "适应力强", "乐观开朗", "独立自主", "直率真诚"],
        weaknesses: ["情绪化", "缺乏耐心", "不够细心", "容易冲动", "难以坚持"]
    },
    AB: {
        name: "AB型血",
        symbol: "🆎",
        traits: {
            positive: ["理性客观", "多才多艺", "善于分析", "包容性强", "独特个性"],
            negative: ["情绪多变", "难以捉摸", "过于理性", "缺乏持久力", "孤独感强"]
        },
        personality: {
            overview: "AB型血的人通常具有双重性格，既有A型的细心又有B型的创造力。他们理性客观，多才多艺，但有时可能显得复杂难懂。",
            work: "善于分析和综合，适合需要多元思维的工作。能够在不同的角色间切换，但可能因为兴趣太广泛而缺乏专注。",
            love: "在感情中比较理性，需要精神上的共鸣。可能显得有些冷淡，但一旦认真起来会非常专一。需要理解和包容的伴侣。",
            social: "善于观察和理解他人，但有时可能显得疏离。喜欢与有趣的人交往，但也需要独处的时间来充电。"
        },
        compatibility: {
            best: ["AB型"],
            good: ["A型", "B型"],
            challenging: ["O型"]
        },
        famous: ["爱因斯坦", "肯尼迪", "玛丽莲·梦露"],
        advice: "学会表达情感，不要过于理性化。培养专注力，选择真正感兴趣的领域深入发展。",
        luckyColors: ["紫色", "银色", "粉色"],
        strengths: ["理性客观", "多才多艺", "善于分析", "包容性强", "独特个性"],
        weaknesses: ["情绪多变", "难以捉摸", "过于理性", "缺乏持久力", "孤独感强"]
    },
    O: {
        name: "O型血",
        symbol: "🅾️",
        traits: {
            positive: ["领导力强", "自信果断", "目标明确", "意志坚强", "热情积极"],
            negative: ["过于强势", "缺乏耐心", "固执己见", "容易冲动", "不够细心"]
        },
        personality: {
            overview: "O型血的人通常具有强烈的领导欲望和自信心，目标明确，意志坚强。他们热情积极，但有时可能过于强势和冲动。",
            work: "天生的领导者，善于制定目标和推动执行。适合管理和决策类工作，但需要学会倾听他人意见，注意团队合作。",
            love: "在感情中比较主动和直接，喜欢掌控关系的主导权。对伴侣有保护欲，但也需要学会给对方空间和自由。",
            social: "天生的领袖气质，容易成为团体的中心。但有时可能过于强势，需要学会更多的包容和理解。"
        },
        compatibility: {
            best: ["O型"],
            good: ["A型", "B型"],
            challenging: ["AB型"]
        },
        famous: ["乔布斯", "奥巴马", "刘德华"],
        advice: "学会倾听他人意见，不要过于固执。培养耐心，注意细节处理。",
        luckyColors: ["红色", "金色", "黑色"],
        strengths: ["领导力强", "自信果断", "目标明确", "意志坚强", "热情积极"],
        weaknesses: ["过于强势", "缺乏耐心", "固执己见", "容易冲动", "不够细心"]
    }
};

// 开始血型性格分析
function startBloodTypeAnalysis() {
    const content = `
        <div class="test-form">
            <h2>🩸 血型性格分析</h2>
            <p>选择你的血型，我们将为你分析基于血型理论的性格特征和行为倾向。</p>

            <div class="question">
                <h4>请选择你的血型：</h4>
                <div class="blood-type-selection">
                    ${Object.entries(bloodTypeData).map(([type, data]) => `
                        <label class="blood-type-option">
                            <input type="radio" name="bloodType" value="${type}">
                            <div class="blood-type-card">
                                <div class="blood-type-symbol">${data.symbol}</div>
                                <div class="blood-type-name">${data.name}</div>
                            </div>
                        </label>
                    `).join('')}
                </div>
            </div>

            <button class="submit-btn" onclick="generateBloodTypeAnalysis()" id="bloodTypeSubmit" disabled>查看分析</button>
        </div>
    `;

    openModal(content);
    setupBloodTypeSelection();
}

// 设置血型选择
function setupBloodTypeSelection() {
    const radios = document.querySelectorAll('input[name="bloodType"]');
    const submitBtn = document.getElementById('bloodTypeSubmit');

    radios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (submitBtn) {
                submitBtn.disabled = false;
            }
        });
    });
}

// 生成血型性格分析
function generateBloodTypeAnalysis() {
    const selected = document.querySelector('input[name="bloodType"]:checked');
    if (!selected) return;

    const bloodType = selected.value;
    const data = bloodTypeData[bloodType];

    const result = {
        bloodType: bloodType,
        data: data,
        date: new Date().toISOString()
    };

    // 保存结果
    saveTestResult('bloodtype', result);

    // 显示结果
    showBloodTypeAnalysis(result);
}

// 显示血型性格分析结果
function showBloodTypeAnalysis(result) {
    const { data } = result;

    const content = `
        <div class="result-card">
            <h3>${data.symbol} ${data.name} 性格分析</h3>

            <div class="blood-type-overview">
                <h4>🎯 性格概述</h4>
                <p>${data.personality.overview}</p>
            </div>

            <div class="traits-analysis">
                <div class="positive-traits">
                    <h4>💪 主要优势</h4>
                    <ul>
                        ${data.strengths.map(strength => `<li>${strength}</li>`).join('')}
                    </ul>
                </div>

                <div class="negative-traits">
                    <h4>⚠️ 需要注意</h4>
                    <ul>
                        ${data.weaknesses.map(weakness => `<li>${weakness}</li>`).join('')}
                    </ul>
                </div>
            </div>

            <div class="detailed-analysis">
                <div class="analysis-item">
                    <h4>💼 工作表现</h4>
                    <p>${data.personality.work}</p>
                </div>

                <div class="analysis-item">
                    <h4>💕 感情态度</h4>
                    <p>${data.personality.love}</p>
                </div>

                <div class="analysis-item">
                    <h4>👥 社交特点</h4>
                    <p>${data.personality.social}</p>
                </div>
            </div>

            <div class="compatibility">
                <h4>💝 血型配对</h4>
                <div class="compatibility-grid">
                    <div class="compatibility-item best">
                        <strong>最佳配对：</strong>${data.compatibility.best.join('、')}
                    </div>
                    <div class="compatibility-item good">
                        <strong>良好配对：</strong>${data.compatibility.good.join('、')}
                    </div>
                    <div class="compatibility-item challenging">
                        <strong>需要磨合：</strong>${data.compatibility.challenging.join('、')}
                    </div>
                </div>
            </div>

            <div class="additional-info">
                <div class="info-item">
                    <h4>🌟 同血型名人</h4>
                    <p>${data.famous.join('、')}</p>
                </div>

                <div class="info-item">
                    <h4>🎨 幸运色彩</h4>
                    <p>${data.luckyColors.join('、')}</p>
                </div>

                <div class="info-item">
                    <h4>💡 发展建议</h4>
                    <p>${data.advice}</p>
                </div>
            </div>

            <button class="submit-btn" onclick="closeModal()" style="margin-top: 2rem;">完成分析</button>
        </div>
    `;

    const modalBody = document.getElementById('modalBody');
    if (modalBody) {
        modalBody.innerHTML = content;
    }
}

// ==================== 手相面相分析系统 ====================

// 手相特征数据
const palmistryFeatures = {
    lifeLine: {
        name: "生命线",
        meanings: {
            long: "生命力旺盛，身体健康，寿命较长",
            short: "生命力一般，需要注意身体保养",
            deep: "体质强健，精力充沛",
            shallow: "体质较弱，容易疲劳",
            curved: "性格温和，适应能力强",
            straight: "性格直率，意志坚定"
        }
    },
    heartLine: {
        name: "感情线",
        meanings: {
            long: "感情丰富，重视爱情和友情",
            short: "理性大于感性，不易动情",
            deep: "感情专一，爱恨分明",
            shallow: "感情变化多，不够稳定",
            curved: "温柔体贴，善解人意",
            straight: "感情直接，表达坦率"
        }
    },
    headLine: {
        name: "智慧线",
        meanings: {
            long: "思维敏捷，智慧超群",
            short: "思维简单直接，不喜复杂",
            deep: "专注力强，思考深入",
            shallow: "思维活跃，但不够专注",
            curved: "富有创造力和想象力",
            straight: "逻辑思维强，理性分析"
        }
    }
};

// 面相特征数据
const faceReadingFeatures = {
    forehead: {
        name: "额头",
        meanings: {
            high: "智慧高，思维能力强，适合从事脑力工作",
            wide: "心胸开阔，包容性强，有领导才能",
            narrow: "思维专注，但可能视野较窄",
            smooth: "性格温和，人际关系良好"
        }
    },
    eyes: {
        name: "眼睛",
        meanings: {
            large: "感情丰富，表达能力强，有艺术天赋",
            small: "思维细致，观察力强，做事谨慎",
            bright: "精神饱满，智慧过人，运势较好",
            deep: "思考深入，有哲学思维"
        }
    },
    nose: {
        name: "鼻子",
        meanings: {
            high: "自尊心强，有领导能力，财运较好",
            straight: "性格正直，做事公正，品格高尚",
            round: "性格温和，人缘好，适合合作",
            pointed: "思维敏锐，但可能过于挑剔"
        }
    }
};

// 开始手相面相分析
function startPalmistryAnalysis() {
    const content = `
        <div class="test-form">
            <h2>🤲 手相面相分析</h2>
            <p>上传你的手掌或面部照片，我们将为你分析相学特征。请确保照片清晰，光线充足。</p>

            <div class="question">
                <h4>选择分析类型：</h4>
                <div class="analysis-type-selection">
                    <label class="analysis-type-option">
                        <input type="radio" name="analysisType" value="palmistry" checked>
                        <div class="type-card">
                            <div class="type-icon">🤲</div>
                            <div class="type-name">手相分析</div>
                            <div class="type-desc">分析手掌纹路和特征</div>
                        </div>
                    </label>
                    <label class="analysis-type-option">
                        <input type="radio" name="analysisType" value="faceReading">
                        <div class="type-card">
                            <div class="type-icon">👤</div>
                            <div class="type-name">面相分析</div>
                            <div class="type-desc">分析面部特征和相貌</div>
                        </div>
                    </label>
                </div>
            </div>

            <div class="question">
                <h4>上传照片：</h4>
                <div class="image-upload-area" id="imageUploadArea">
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                    <div class="upload-placeholder" onclick="document.getElementById('imageInput').click()">
                        <div class="upload-icon">📷</div>
                        <p>点击上传照片</p>
                        <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
                    </div>
                    <div class="image-preview" id="imagePreview" style="display: none;">
                        <img id="previewImg" alt="预览图片">
                        <button type="button" onclick="removeImage()">删除</button>
                    </div>
                </div>
            </div>

            <div class="question">
                <h4>补充信息（可选）：</h4>
                <div class="additional-info">
                    <label>
                        性别：
                        <select id="gender">
                            <option value="">请选择</option>
                            <option value="male">男</option>
                            <option value="female">女</option>
                        </select>
                    </label>
                    <label>
                        年龄：
                        <input type="number" id="age" placeholder="请输入年龄" min="1" max="120">
                    </label>
                </div>
            </div>

            <button class="submit-btn" onclick="analyzePalmistryImage()" id="palmistrySubmit" disabled>开始分析</button>
        </div>
    `;

    openModal(content);
    setupImageUpload();
}

// 设置图片上传
function setupImageUpload() {
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const uploadPlaceholder = document.querySelector('.upload-placeholder');
    const submitBtn = document.getElementById('palmistrySubmit');

    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // 检查文件大小（5MB限制）
                if (file.size > 5 * 1024 * 1024) {
                    showMessage('文件大小不能超过5MB，请选择其他图片。', 'error');
                    return;
                }

                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    showMessage('请选择有效的图片文件。', 'error');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    if (previewImg) {
                        previewImg.src = e.target.result;
                    }
                    if (uploadPlaceholder) {
                        uploadPlaceholder.style.display = 'none';
                    }
                    if (imagePreview) {
                        imagePreview.style.display = 'block';
                    }
                    if (submitBtn) {
                        submitBtn.disabled = false;
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// 删除图片
function removeImage() {
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');
    const uploadPlaceholder = document.querySelector('.upload-placeholder');
    const submitBtn = document.getElementById('palmistrySubmit');

    if (imageInput) imageInput.value = '';
    if (imagePreview) imagePreview.style.display = 'none';
    if (uploadPlaceholder) uploadPlaceholder.style.display = 'block';
    if (submitBtn) submitBtn.disabled = true;
}

// 分析手相面相图片
function analyzePalmistryImage() {
    const analysisType = document.querySelector('input[name="analysisType"]:checked')?.value;
    const imageInput = document.getElementById('imageInput');
    const gender = document.getElementById('gender')?.value;
    const age = document.getElementById('age')?.value;

    if (!imageInput?.files[0]) {
        showMessage('请先上传图片。', 'error');
        return;
    }

    // 模拟AI分析过程
    showMessage('正在分析图片，请稍候...', 'info');

    setTimeout(() => {
        const result = generatePalmistryAnalysis(analysisType, gender, age);
        saveTestResult('palmistry', result);
        showPalmistryAnalysis(result);
    }, 2000);
}

// 生成手相面相分析结果
function generatePalmistryAnalysis(type, gender, age) {
    const isHandAnalysis = type === 'palmistry';

    if (isHandAnalysis) {
        return {
            type: 'palmistry',
            title: '手相分析报告',
            features: {
                lifeLine: {
                    name: '生命线',
                    characteristics: ['较长', '深刻', '弯曲'],
                    meaning: '生命力旺盛，身体健康状况良好，性格温和，适应能力强。'
                },
                heartLine: {
                    name: '感情线',
                    characteristics: ['清晰', '略弯', '延伸至食指'],
                    meaning: '感情丰富，重视爱情，温柔体贴，善解人意，感情专一。'
                },
                headLine: {
                    name: '智慧线',
                    characteristics: ['深刻', '略长', '轻微弯曲'],
                    meaning: '思维敏捷，智慧超群，富有创造力和想象力，专注力强。'
                }
            },
            overall: '从整体手相来看，你是一个生命力旺盛、感情丰富、智慧超群的人。生命线显示你有良好的健康基础，感情线表明你重视感情且专一，智慧线则显示你思维敏捷，富有创造力。建议在保持健康的同时，充分发挥你的智慧和创造力。',
            advice: '保持积极的生活态度，注重身体健康，在感情中保持真诚，在工作中发挥创造力。',
            gender: gender,
            age: age,
            date: new Date().toISOString()
        };
    } else {
        return {
            type: 'faceReading',
            title: '面相分析报告',
            features: {
                forehead: {
                    name: '额头',
                    characteristics: ['宽阔', '饱满', '光滑'],
                    meaning: '智慧高，思维能力强，心胸开阔，包容性强，有领导才能。'
                },
                eyes: {
                    name: '眼睛',
                    characteristics: ['明亮', '有神', '大小适中'],
                    meaning: '精神饱满，智慧过人，感情丰富，表达能力强，运势较好。'
                },
                nose: {
                    name: '鼻子',
                    characteristics: ['挺直', '鼻梁高', '鼻头圆润'],
                    meaning: '性格正直，做事公正，自尊心强，有领导能力，财运较好。'
                }
            },
            overall: '从整体面相来看，你具有很好的智慧和领导潜质。额头宽阔显示你思维能力强，眼睛明亮表明你精神饱满，鼻子挺直说明你性格正直。这些特征组合在一起，预示着你在事业上会有不错的发展。',
            advice: '充分发挥你的智慧和领导才能，保持正直的品格，在人际交往中展现你的包容性。',
            gender: gender,
            age: age,
            date: new Date().toISOString()
        };
    }
}

// 显示手相面相分析结果
function showPalmistryAnalysis(result) {
    const content = `
        <div class="result-card">
            <h3>🔍 ${result.title}</h3>

            <div class="analysis-features">
                ${Object.entries(result.features).map(([key, feature]) => `
                    <div class="feature-analysis">
                        <h4>${feature.name}</h4>
                        <div class="characteristics">
                            <strong>特征：</strong>${feature.characteristics.join('、')}
                        </div>
                        <div class="meaning">
                            <strong>解读：</strong>${feature.meaning}
                        </div>
                    </div>
                `).join('')}
            </div>

            <div class="overall-analysis">
                <h4>🌟 综合分析</h4>
                <p>${result.overall}</p>
            </div>

            <div class="advice-section">
                <h4>💡 建议指导</h4>
                <p>${result.advice}</p>
            </div>

            <div class="analysis-note">
                <p><small>* 此分析基于传统相学理论，仅供参考娱乐，不作为人生决策依据。</small></p>
            </div>

            <button class="submit-btn" onclick="closeModal()" style="margin-top: 2rem;">完成分析</button>
        </div>
    `;

    const modalBody = document.getElementById('modalBody');
    if (modalBody) {
        modalBody.innerHTML = content;
    }
}

// ==================== 易经八卦占卜系统 ====================

// 八卦基础数据
const bagua = {
    qian: { name: "乾", symbol: "☰", element: "天", nature: "刚健", meaning: "创造、领导、父亲" },
    kun: { name: "坤", symbol: "☷", element: "地", nature: "柔顺", meaning: "包容、顺从、母亲" },
    zhen: { name: "震", symbol: "☳", element: "雷", nature: "动", meaning: "震动、行动、长男" },
    xun: { name: "巽", symbol: "☴", element: "风", nature: "入", meaning: "渗透、温和、长女" },
    kan: { name: "坎", symbol: "☵", element: "水", nature: "陷", meaning: "险难、智慧、中男" },
    li: { name: "离", symbol: "☲", element: "火", nature: "丽", meaning: "光明、美丽、中女" },
    gen: { name: "艮", symbol: "☶", element: "山", nature: "止", meaning: "停止、稳定、少男" },
    dui: { name: "兑", symbol: "☱", element: "泽", nature: "悦", meaning: "喜悦、交流、少女" }
};

// 64卦数据（简化版，包含主要卦象）
const hexagrams = {
    1: { name: "乾为天", upper: "qian", lower: "qian", meaning: "元亨利贞。大吉大利，但需要持之以恒。", advice: "天行健，君子以自强不息。现在是展现领导力的时候。" },
    2: { name: "坤为地", upper: "kun", lower: "kun", meaning: "元亨，利牝马之贞。顺应时势，以柔克刚。", advice: "地势坤，君子以厚德载物。保持谦逊和包容的态度。" },
    3: { name: "水雷屯", upper: "kan", lower: "zhen", meaning: "元亨利贞，勿用有攸往。初始困难，需要耐心。", advice: "万事开头难，需要积累实力，不宜急进。" },
    4: { name: "山水蒙", upper: "gen", lower: "kan", meaning: "亨，匪我求童蒙，童蒙求我。学习和启蒙的时期。", advice: "保持学习的心态，虚心求教，智慧自然增长。" },
    5: { name: "水天需", upper: "kan", lower: "qian", meaning: "有孚，光亨，贞吉。等待时机，保持信心。", advice: "时机未到，需要耐心等待，但要做好准备。" },
    6: { name: "天水讼", upper: "qian", lower: "kan", meaning: "有孚，窒惕，中吉。避免争执，寻求和解。", advice: "化干戈为玉帛，通过沟通解决分歧。" },
    7: { name: "地水师", upper: "kun", lower: "kan", meaning: "贞，丈人吉，无咎。团结协作，共同目标。", advice: "众志成城，在团队中发挥作用，听从指挥。" },
    8: { name: "水地比", upper: "kan", lower: "kun", meaning: "吉，原筮元永贞。亲密合作，相互支持。", advice: "建立良好的人际关系，互相帮助，共同进步。" },
    11: { name: "地天泰", upper: "kun", lower: "qian", meaning: "小往大来，吉亨。通泰顺利，万事如意。", advice: "运势极佳，但要居安思危，保持谦逊。" },
    12: { name: "天地否", upper: "qian", lower: "kun", meaning: "匪人不利君子贞。阻塞不通，需要坚持。", advice: "困难时期，坚持正道，等待转机。" },
    13: { name: "天火同人", upper: "qian", lower: "li", meaning: "同人于野，亨。团结合作，共同理想。", advice: "与志同道合的人合作，实现共同目标。" },
    14: { name: "火天大有", upper: "li", lower: "qian", meaning: "元亨。大有收获，成就显著。", advice: "成功在望，但要懂得分享，不可独享。" },
    15: { name: "地山谦", upper: "kun", lower: "gen", meaning: "亨，君子有终。谦逊有礼，必有好结果。", advice: "谦虚使人进步，保持低调，德行自然彰显。" },
    16: { name: "雷地豫", upper: "zhen", lower: "kun", meaning: "利建侯行师。愉悦和谐，众人响应。", advice: "保持乐观积极的态度，能够感染和带动他人。" },
    20: { name: "风地观", upper: "xun", lower: "kun", meaning: "盥而不荐，有孚颙若。观察学习，深入思考。", advice: "多观察，少行动，从中学习经验和智慧。" },
    23: { name: "山地剥", upper: "gen", lower: "kun", meaning: "不利有攸往。剥落衰败，需要忍耐。", advice: "低潮期，保存实力，等待重新开始的机会。" },
    24: { name: "地雷复", upper: "kun", lower: "zhen", meaning: "亨，出入无疾。复苏回转，重新开始。", advice: "困难过去，新的机会来临，要把握时机。" },
    25: { name: "天雷无妄", upper: "qian", lower: "zhen", meaning: "元亨利贞。顺应自然，不可妄为。", advice: "顺其自然，不要强求，真诚待人处事。" },
    26: { name: "山天大畜", upper: "gen", lower: "qian", meaning: "利贞，不家食吉。积累实力，厚积薄发。", advice: "现在是积累的时期，充实自己，为将来做准备。" },
    27: { name: "山雷颐", upper: "gen", lower: "zhen", meaning: "贞吉，观颐。养生修身，注重品德。", advice: "注意身心健康，修养品德，言行要谨慎。" },
    28: { name: "泽风大过", upper: "dui", lower: "xun", meaning: "栋桡，利有攸往。过度状态，需要调整。", advice: "事情过头了，需要适度调整，寻求平衡。" },
    29: { name: "坎为水", upper: "kan", lower: "kan", meaning: "有孚，维心亨。险中求胜，智慧应对。", advice: "面临困难，但要保持信心，用智慧化解危机。" },
    30: { name: "离为火", upper: "li", lower: "li", meaning: "利贞，亨。光明磊落，前途光明。", advice: "保持正直和光明的品格，前途一片光明。" }
};

// 开始易经八卦占卜
function startIChingDivination() {
    const content = `
        <div class="test-form">
            <h2>☯️ 易经八卦占卜</h2>
            <p>易经是中华文化的瑰宝，通过八卦占卜可以获得人生指引。请静心思考你的问题，然后进行占卜。</p>

            <div class="question">
                <h4>请输入你想要占卜的问题：</h4>
                <textarea id="ichingQuestion" placeholder="例如：我在事业发展上应该如何选择？" rows="3"></textarea>
            </div>

            <div class="question">
                <h4>选择占卜方式：</h4>
                <div class="divination-methods">
                    <label class="method-option selected">
                        <input type="radio" name="method" value="coins" checked>
                        <div class="method-card">
                            <div class="method-icon">🪙</div>
                            <div class="method-name">投掷铜钱</div>
                            <div class="method-desc">传统的三枚铜钱占卜法</div>
                        </div>
                    </label>
                    <label class="method-option">
                        <input type="radio" name="method" value="random">
                        <div class="method-card">
                            <div class="method-icon">🎲</div>
                            <div class="method-name">随机起卦</div>
                            <div class="method-desc">现代随机数字起卦法</div>
                        </div>
                    </label>
                </div>
            </div>

            <div id="coinDivination" class="coin-divination">
                <h4>请连续投掷三枚铜钱六次：</h4>
                <p>每次投掷后，记录正面（阳）和反面（阴）的数量</p>
                <div class="coin-throws">
                    ${Array.from({length: 6}, (_, i) => `
                        <div class="throw-round">
                            <h5>第${i + 1}次投掷：</h5>
                            <div class="coins">
                                ${Array.from({length: 3}, (_, j) => `
                                    <div class="coin" onclick="flipCoin(${i}, ${j})" id="coin-${i}-${j}">
                                        <div class="coin-face">?</div>
                                    </div>
                                `).join('')}
                            </div>
                            <div class="throw-result" id="result-${i}"></div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <button class="submit-btn" onclick="generateIChingResult()" id="ichingSubmit" disabled>查看卦象</button>
        </div>
    `;

    openModal(content);
    setupIChingDivination();
}

// 设置易经占卜
function setupIChingDivination() {
    const methodRadios = document.querySelectorAll('input[name="method"]');
    methodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const coinDiv = document.getElementById('coinDivination');
            if (this.value === 'coins') {
                coinDiv.style.display = 'block';
            } else {
                coinDiv.style.display = 'none';
                // 随机方式直接启用按钮
                const submitBtn = document.getElementById('ichingSubmit');
                if (submitBtn) submitBtn.disabled = false;
            }
        });
    });
}

// 投掷铜钱状态
let coinThrows = Array(6).fill(null).map(() => Array(3).fill(null));
let completedThrows = 0;

// 翻转铜钱
function flipCoin(throwIndex, coinIndex) {
    const coin = document.getElementById(`coin-${throwIndex}-${coinIndex}`);
    const face = coin.querySelector('.coin-face');

    // 随机决定正反面
    const isHeads = Math.random() < 0.5;
    coinThrows[throwIndex][coinIndex] = isHeads ? 1 : 0;

    // 更新显示
    face.textContent = isHeads ? '阳' : '阴';
    face.style.backgroundColor = isHeads ? '#FFD700' : '#C0C0C0';

    // 检查这一轮是否完成
    if (coinThrows[throwIndex].every(c => c !== null)) {
        updateThrowResult(throwIndex);
        checkAllThrowsComplete();
    }
}

// 更新投掷结果
function updateThrowResult(throwIndex) {
    const result = document.getElementById(`result-${throwIndex}`);
    const yangCount = coinThrows[throwIndex].filter(c => c === 1).length;
    const yinCount = 3 - yangCount;

    let lineType, symbol;
    if (yangCount === 3) {
        lineType = "老阳";
        symbol = "━━━";
    } else if (yangCount === 2) {
        lineType = "少阴";
        symbol = "━ ━";
    } else if (yangCount === 1) {
        lineType = "少阳";
        symbol = "━━━";
    } else {
        lineType = "老阴";
        symbol = "━ ━";
    }

    result.innerHTML = `<strong>${lineType}</strong> ${symbol}`;
    result.style.color = yangCount >= 2 ? '#FF6B6B' : '#4ECDC4';
}

// 检查所有投掷是否完成
function checkAllThrowsComplete() {
    completedThrows = coinThrows.filter(throw_ => throw_.every(c => c !== null)).length;
    const submitBtn = document.getElementById('ichingSubmit');

    if (submitBtn) {
        submitBtn.disabled = completedThrows < 6;
        submitBtn.textContent = completedThrows < 6 ? `已完成 ${completedThrows}/6 次投掷` : '查看卦象';
    }
}

// 生成易经占卜结果
function generateIChingResult() {
    const question = document.getElementById('ichingQuestion')?.value || "未指定问题";
    const method = document.querySelector('input[name="method"]:checked')?.value;

    let hexagramNumber;
    if (method === 'coins') {
        // 基于投掷结果生成卦象
        hexagramNumber = calculateHexagramFromCoins();
    } else {
        // 随机生成卦象
        hexagramNumber = getRandomInt(1, 30); // 使用我们定义的卦象范围
    }

    const hexagram = hexagrams[hexagramNumber];
    if (!hexagram) {
        showMessage('卦象生成失败，请重试。', 'error');
        return;
    }

    const result = {
        question: question,
        method: method,
        hexagramNumber: hexagramNumber,
        hexagram: hexagram,
        upperBagua: bagua[hexagram.upper],
        lowerBagua: bagua[hexagram.lower],
        date: new Date().toISOString()
    };

    // 保存结果
    saveTestResult('iching', result);

    // 显示结果
    showIChingResult(result);
}

// 从铜钱投掷计算卦象
function calculateHexagramFromCoins() {
    // 简化计算，基于投掷结果的模式
    let total = 0;
    coinThrows.forEach((throw_, index) => {
        const yangCount = throw_.filter(c => c === 1).length;
        total += yangCount * (index + 1);
    });

    // 映射到我们的卦象范围
    return (total % 30) + 1;
}

// 显示易经占卜结果
function showIChingResult(result) {
    const { hexagram, upperBagua, lowerBagua } = result;

    const content = `
        <div class="result-card">
            <h3>☯️ 易经占卜结果</h3>

            <div class="iching-question">
                <h4>占卜问题：</h4>
                <p>${result.question}</p>
            </div>

            <div class="hexagram-display">
                <div class="hexagram-info">
                    <h3>${hexagram.name}</h3>
                    <div class="bagua-structure">
                        <div class="upper-bagua">
                            <div class="bagua-symbol">${upperBagua.symbol}</div>
                            <div class="bagua-info">
                                <strong>${upperBagua.name}</strong> (${upperBagua.element})
                                <br><small>${upperBagua.meaning}</small>
                            </div>
                        </div>
                        <div class="lower-bagua">
                            <div class="bagua-symbol">${lowerBagua.symbol}</div>
                            <div class="bagua-info">
                                <strong>${lowerBagua.name}</strong> (${lowerBagua.element})
                                <br><small>${lowerBagua.meaning}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="hexagram-meaning">
                <h4>🔍 卦象解读</h4>
                <p>${hexagram.meaning}</p>
            </div>

            <div class="hexagram-advice">
                <h4>💡 指导建议</h4>
                <p>${hexagram.advice}</p>
            </div>

            <div class="iching-note">
                <p><small>* 易经占卜源于古代智慧，重在启发思考，请结合实际情况理性判断。</small></p>
            </div>

            <button class="submit-btn" onclick="closeModal()" style="margin-top: 2rem;">完成占卜</button>
        </div>
    `;

    const modalBody = document.getElementById('modalBody');
    if (modalBody) {
        modalBody.innerHTML = content;
    }
}

// ==================== AI分析能力模拟系统 ====================

// AI分析模板
const aiAnalysisTemplates = {
    mbti: {
        insights: [
            "根据你的MBTI类型，你在团队中往往扮演着重要的角色。你的性格特质使你能够在特定的环境中发挥最大的潜力。",
            "你的人格类型显示了独特的思维模式和行为倾向。这些特质在正确的环境下可以成为你的巨大优势。",
            "从心理学角度来看，你的性格类型反映了深层的认知偏好和价值观系统。",
            "你的MBTI结果揭示了你在处理信息、做决定和与他人互动时的自然倾向。"
        ],
        recommendations: [
            "建议你在日常生活中更多地关注自己的优势领域，同时也要意识到可能的盲点。",
            "考虑寻找能够发挥你天然优势的工作环境和人际关系。",
            "定期进行自我反思，了解自己在不同情况下的反应模式。",
            "与不同性格类型的人交往，可以帮助你获得新的视角和成长机会。"
        ]
    },
    zodiac: {
        insights: [
            "你的星座特质与当前的宇宙能量形成了独特的共振。这种能量场会影响你的情绪状态和决策能力。",
            "从占星学的角度来看，你的星座位置反映了出生时天体的排列对你性格的影响。",
            "你的星座元素特质在当前时期特别活跃，这可能会带来新的机遇和挑战。",
            "星座的影响往往是潜移默化的，通过观察自己的行为模式可以更好地理解这些影响。"
        ],
        recommendations: [
            "利用你星座的自然优势，在合适的时机采取行动。",
            "注意星座特质可能带来的挑战，提前做好心理准备。",
            "与其他星座的人交流，可以帮助你获得不同的视角。",
            "定期关注星象变化，调整自己的行为和计划。"
        ]
    },
    tarot: {
        insights: [
            "塔罗牌反映了你潜意识中的智慧和直觉。这些符号和意象与你当前的生活状况产生了深层的共鸣。",
            "从心理学角度来看，塔罗牌可以作为一面镜子，帮助你更好地了解自己的内心世界。",
            "你抽到的牌组合显示了一个完整的故事，反映了你生活中的重要主题和转折点。",
            "塔罗的智慧在于启发你思考，而不是预测固定的未来。"
        ],
        recommendations: [
            "相信你的直觉，它往往能指引你找到正确的方向。",
            "保持开放的心态，接受生活中的变化和挑战。",
            "定期进行内省，了解自己的真实需求和愿望。",
            "将塔罗的启示与实际行动相结合，创造积极的改变。"
        ]
    },
    bloodtype: {
        insights: [
            "血型性格理论虽然缺乏严格的科学依据，但它反映了人们对性格分类的需求和理解。",
            "你的血型特征可能与你的某些行为模式相符，这可能是文化影响和自我暗示的结果。",
            "从社会心理学的角度来看，血型理论提供了一种简单的自我认知框架。",
            "重要的是要记住，个人的性格远比任何分类系统都要复杂和独特。"
        ],
        recommendations: [
            "将血型分析作为自我了解的一个参考，而不是绝对的标准。",
            "关注自己的实际行为和感受，而不是被标签所限制。",
            "保持开放的心态，接受自己性格的多面性和复杂性。",
            "与他人交往时，避免基于血型的刻板印象。"
        ]
    },
    palmistry: {
        insights: [
            "手相学是一门古老的艺术，它试图通过手部特征来解读人的性格和命运。",
            "虽然缺乏科学依据，但手相分析可以作为自我反思的工具。",
            "你的手部特征可能反映了某些生活习惯和性格倾向。",
            "重要的是要记住，你的未来掌握在自己手中，而不是手纹中。"
        ],
        recommendations: [
            "将手相分析作为娱乐和自我探索的方式。",
            "专注于可以改变的行为和习惯，而不是固定的特征。",
            "保持积极的心态，相信自己有能力创造理想的未来。",
            "结合其他自我认知工具，获得更全面的自我了解。"
        ]
    },
    iching: {
        insights: [
            "易经是中华文化的瑰宝，它提供了一种独特的思维方式和人生哲学。",
            "你得到的卦象反映了当前情况的复杂性和多面性。",
            "易经的智慧在于教导我们如何在变化中保持平衡和智慧。",
            "卦象的解读需要结合具体情况和个人经验来理解。"
        ],
        recommendations: [
            "将易经的指导作为思考问题的新角度。",
            "保持谦逊和学习的心态，接受生活的不确定性。",
            "在做决定时考虑多个因素，寻求平衡和和谐。",
            "定期反思自己的行为和选择，从中获得智慧。"
        ]
    }
};

// 生成AI深度分析
function generateAIAnalysis(testType, result) {
    const template = aiAnalysisTemplates[testType];
    if (!template) return null;

    const insight = getRandomElement(template.insights);
    const recommendation = getRandomElement(template.recommendations);

    return {
        insight: insight,
        recommendation: recommendation,
        confidence: getRandomInt(85, 95), // 模拟AI置信度
        analysisTime: new Date().toISOString()
    };
}

// 增强现有的结果显示函数，添加AI分析
function enhanceResultWithAI(testType, result) {
    const aiAnalysis = generateAIAnalysis(testType, result);
    if (aiAnalysis) {
        result.aiAnalysis = aiAnalysis;
    }
    return result;
}

// 显示AI分析部分的HTML
function getAIAnalysisHTML(aiAnalysis) {
    if (!aiAnalysis) return '';

    return `
        <div class="ai-analysis-section">
            <h4>🤖 AI深度分析</h4>
            <div class="ai-insight">
                <h5>💡 深度洞察</h5>
                <p>${aiAnalysis.insight}</p>
            </div>
            <div class="ai-recommendation">
                <h5>🎯 个性化建议</h5>
                <p>${aiAnalysis.recommendation}</p>
            </div>
            <div class="ai-confidence">
                <small>AI分析置信度: ${aiAnalysis.confidence}%</small>
            </div>
        </div>
    `;
}

// 修改保存测评结果函数，添加AI分析
function saveTestResultWithAI(testType, result) {
    const enhancedResult = enhanceResultWithAI(testType, result);
    saveTestResult(testType, enhancedResult);
    return enhancedResult;
}

// ==================== 数据存储和用户系统 ====================

// 用户偏好设置
const userPreferences = {
    theme: 'default',
    notifications: true,
    defaultZodiac: null,
    language: 'zh-CN'
};

// 加载用户偏好
function loadUserPreferences() {
    const saved = localStorage.getItem('xingyun_preferences');
    if (saved) {
        try {
            Object.assign(userPreferences, JSON.parse(saved));
        } catch (e) {
            console.error('加载用户偏好失败:', e);
        }
    }
}

// 保存用户偏好
function saveUserPreferences() {
    try {
        localStorage.setItem('xingyun_preferences', JSON.stringify(userPreferences));
    } catch (e) {
        console.error('保存用户偏好失败:', e);
    }
}

// 获取用户测评历史
function getUserTestHistory(testType = null) {
    if (testType) {
        return testResults[testType] || [];
    }
    return testResults;
}

// 获取用户统计信息
function getUserStats() {
    const stats = {
        totalTests: 0,
        testsByType: {},
        lastTestDate: null,
        favoriteTest: null
    };

    Object.entries(testResults).forEach(([type, results]) => {
        stats.totalTests += results.length;
        stats.testsByType[type] = results.length;

        if (results.length > 0) {
            const lastTest = results[results.length - 1];
            if (!stats.lastTestDate || new Date(lastTest.timestamp) > new Date(stats.lastTestDate)) {
                stats.lastTestDate = lastTest.timestamp;
            }
        }
    });

    // 找出最常用的测评类型
    let maxCount = 0;
    Object.entries(stats.testsByType).forEach(([type, count]) => {
        if (count > maxCount) {
            maxCount = count;
            stats.favoriteTest = type;
        }
    });

    return stats;
}

// 清除用户数据
function clearUserData() {
    if (confirm('确定要清除所有用户数据吗？此操作不可撤销。')) {
        localStorage.removeItem('xingyun_user_profile');
        localStorage.removeItem('xingyun_test_results');
        localStorage.removeItem('xingyun_preferences');

        // 重置全局变量
        Object.keys(userProfile).forEach(key => delete userProfile[key]);
        Object.keys(testResults).forEach(key => delete testResults[key]);
        Object.assign(userPreferences, {
            theme: 'default',
            notifications: true,
            defaultZodiac: null,
            language: 'zh-CN'
        });

        showMessage('用户数据已清除', 'success');

        // 刷新页面
        setTimeout(() => {
            location.reload();
        }, 1500);
    }
}

// 导出用户数据
function exportUserData() {
    const data = {
        profile: userProfile,
        results: testResults,
        preferences: userPreferences,
        exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `xingyun_data_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showMessage('数据导出成功', 'success');
}

// 初始化用户系统
function initializeUserSystem() {
    loadUserPreferences();

    // 如果是首次访问，显示欢迎信息
    if (!localStorage.getItem('xingyun_visited')) {
        setTimeout(() => {
            showMessage('欢迎来到星运测评！开始你的自我探索之旅吧。', 'info');
            localStorage.setItem('xingyun_visited', 'true');
        }, 1000);
    }
}

// 在应用初始化时调用
document.addEventListener('DOMContentLoaded', function() {
    initializeUserSystem();
});

// ==================== 移动端优化 ====================

// 检测移动设备
function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 移动端触摸优化
function setupMobileOptimizations() {
    if (isMobileDevice()) {
        // 添加移动端特定的CSS类
        document.body.classList.add('mobile-device');

        // 优化触摸事件
        setupTouchEvents();

        // 防止双击缩放
        preventDoubleClickZoom();

        // 优化模态框在移动端的显示
        optimizeModalForMobile();
    }
}

// 设置触摸事件
function setupTouchEvents() {
    // 为所有按钮添加触摸反馈
    const buttons = document.querySelectorAll('button, .btn-primary, .btn-secondary, .test-btn');
    buttons.forEach(button => {
        button.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });

        button.addEventListener('touchend', function() {
            this.style.transform = '';
        });
    });
}

// 防止双击缩放
function preventDoubleClickZoom() {
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
}

// 优化移动端模态框
function optimizeModalForMobile() {
    const modal = document.getElementById('testModal');
    if (modal && isMobileDevice()) {
        modal.addEventListener('touchmove', function(e) {
            // 防止背景滚动
            if (e.target === modal) {
                e.preventDefault();
            }
        });
    }
}

// 添加移动端手势支持
function addSwipeSupport() {
    let startX, startY, endX, endY;

    document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    });

    document.addEventListener('touchend', function(e) {
        endX = e.changedTouches[0].clientX;
        endY = e.changedTouches[0].clientY;
        handleSwipe();
    });

    function handleSwipe() {
        const deltaX = endX - startX;
        const deltaY = endY - startY;

        // 检测水平滑动
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
            if (deltaX > 0) {
                // 向右滑动
                handleSwipeRight();
            } else {
                // 向左滑动
                handleSwipeLeft();
            }
        }
    }

    function handleSwipeRight() {
        // 可以添加向右滑动的功能，比如返回上一页
        console.log('向右滑动');
    }

    function handleSwipeLeft() {
        // 可以添加向左滑动的功能
        console.log('向左滑动');
    }
}

// ==================== 性能优化 ====================

// 图片懒加载
function setupLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 优化滚动性能
function optimizeScrollPerformance() {
    const handleScroll = throttle(() => {
        // 滚动时的处理逻辑
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // 导航栏透明度变化
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (scrollTop > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        }
    }, 16); // 约60fps

    window.addEventListener('scroll', handleScroll);
}

// ==================== 错误处理和日志 ====================

// 全局错误处理
function setupErrorHandling() {
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
        // 可以发送错误报告到服务器
        logError('JavaScript Error', event.error.message, event.filename, event.lineno);
    });

    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
        logError('Promise Rejection', event.reason);
    });
}

// 错误日志记录
function logError(type, message, filename = '', lineno = 0) {
    const errorLog = {
        type: type,
        message: message,
        filename: filename,
        lineno: lineno,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
    };

    // 保存到本地存储
    const errors = JSON.parse(localStorage.getItem('xingyun_errors') || '[]');
    errors.push(errorLog);

    // 只保留最近50个错误
    if (errors.length > 50) {
        errors.splice(0, errors.length - 50);
    }

    localStorage.setItem('xingyun_errors', JSON.stringify(errors));
}

// ==================== 最终初始化 ====================

// 完整的应用初始化
function initializeCompleteApp() {
    try {
        // 基础功能初始化
        initializeApp();

        // 用户系统初始化
        initializeUserSystem();

        // 移动端优化
        setupMobileOptimizations();

        // 性能优化
        setupLazyLoading();
        optimizeScrollPerformance();

        // 手势支持
        if (isMobileDevice()) {
            addSwipeSupport();
        }

        // 错误处理
        setupErrorHandling();

        console.log('星运测评应用初始化完成');

    } catch (error) {
        console.error('应用初始化失败:', error);
        logError('Initialization Error', error.message);
    }
}

// 替换原有的初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeCompleteApp();
});
