/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: #667eea;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #667eea;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* 主页面样式 */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 20px 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: white;
    color: #667eea;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
}

/* 浮动卡片动画 */
.hero-visual {
    position: relative;
    height: 400px;
}

.floating-cards {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-cards .card {
    position: absolute;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 600;
    animation: float 6s ease-in-out infinite;
}

.card.mbti {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card.tarot {
    top: 60%;
    left: 60%;
    animation-delay: 1.5s;
}

.card.zodiac {
    top: 10%;
    right: 20%;
    animation-delay: 3s;
}

.card.palmistry {
    bottom: 20%;
    left: 30%;
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* 测评中心样式 */
.tests-section {
    padding: 100px 0;
    background: white;
}

.tests-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 4rem;
}

.tests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.test-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.test-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.test-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.test-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.test-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.test-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.test-stats span {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #666;
}

.test-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* 每日运势样式 */
.daily-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.daily-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.fortune-selector {
    text-align: center;
    margin-bottom: 3rem;
}

.fortune-selector h3 {
    font-size: 1.5rem;
    margin-bottom: 2rem;
}

.zodiac-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.zodiac-btn {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.zodiac-btn:hover, .zodiac-btn.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.fortune-display {
    max-width: 800px;
    margin: 0 auto;
}

.fortune-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.fortune-item {
    margin-bottom: 2rem;
}

.fortune-item h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.fortune-stars {
    display: flex;
    gap: 0.2rem;
    margin-bottom: 0.5rem;
}

.star {
    color: #ffd700;
    font-size: 1.2rem;
}

.star.empty {
    color: rgba(255, 255, 255, 0.3);
}

.lucky-colors {
    margin-top: 2rem;
}

.color-palette {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

.color-item {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 关于我们样式 */
.about-section {
    padding: 100px 0;
    background: white;
}

.about-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #666;
    line-height: 1.8;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
}

.feature h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature p {
    color: #666;
}

/* 页脚样式 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #ecf0f1;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close:hover {
    color: #000;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .tests-grid {
        grid-template-columns: 1fr;
    }
    
    .zodiac-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .features {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
    
    .btn-primary, .btn-secondary {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
    
    .tests-section h2, .daily-section h2, .about-section h2 {
        font-size: 2rem;
    }
    
    .test-card {
        padding: 1.5rem;
    }
    
    .modal-content {
        margin: 10% auto;
        padding: 1.5rem;
        width: 95%;
    }
}

/* 测评表单样式 */
.test-form {
    max-width: 600px;
    margin: 0 auto;
}

.question {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 15px;
}

.question h4 {
    margin-bottom: 1rem;
    color: #333;
}

.options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.option:hover {
    background: rgba(102, 126, 234, 0.1);
}

.option input[type="radio"] {
    margin-right: 0.5rem;
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 2rem;
}

.result-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
}

.result-card h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.result-card .personality-type {
    font-size: 3rem;
    font-weight: bold;
    margin: 1rem 0;
}

.result-card .description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.traits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.trait {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.trait h4 {
    margin-bottom: 0.5rem;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.2);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: white;
    border-radius: 4px;
    transition: width 0.5s ease;
}